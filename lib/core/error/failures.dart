import 'package:equatable/equatable.dart';

/// Base class for all failures
abstract class Failure extends Equatable {
  final String message;
  final String? code;

  const Failure({required this.message, this.code});

  @override
  List<Object?> get props => [message, code];
}

/// Server failure (HTTP 5xx errors)
class ServerFailure extends Failure {
  const ServerFailure({required super.message, super.code});
}

/// Client failure (HTTP 4xx errors)
class ClientFailure extends Failure {
  const ClientFailure({required super.message, super.code});
}

/// Cache failure
class CacheFailure extends Failure {
  const CacheFailure({required super.message, super.code});
}

/// Network failure
class NetworkFailure extends Failure {
  const NetworkFailure({required super.message, super.code});
}

/// Validation failure (HTTP 422 errors with validation_errors)
class ValidationFailure extends Failure {
  final Map<String, List<String>>? validationErrors;

  const ValidationFailure({
    required super.message,
    super.code,
    this.validationErrors,
  });

  @override
  List<Object?> get props => [message, code, validationErrors];
}

/// Authentication/Authorization failure (HTTP 401/403 errors)
class AuthFailure extends Failure {
  const AuthFailure({required super.message, super.code});
}

/// Response parsing failure
class ParseFailure extends Failure {
  const ParseFailure({required super.message, super.code});
}

/// Business logic failure
class BusinessLogicFailure extends Failure {
  const BusinessLogicFailure({required super.message, super.code});
}

/// Unhandled failure
class UnhandledFailure extends Failure {
  const UnhandledFailure({required super.message, super.code});
}
