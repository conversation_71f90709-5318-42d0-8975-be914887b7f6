/// Base exception class for all API-related exceptions
abstract class ApiException implements Exception {
  final String? code;
  final String? message;
  final dynamic data;

  const ApiException({this.code, this.message, this.data});

  @override
  String toString() => 'ApiException(code: $code, message: $message)';
}

/// Server-side error exception (HTTP 5xx errors)
class ServerException extends ApiException {
  const ServerException({super.code, super.message, super.data});

  @override
  String toString() => 'ServerException(code: $code, message: $message)';
}

/// Client-side error exception (HTTP 4xx errors)
class ClientException extends ApiException {
  const ClientException({super.code, super.message, super.data});

  @override
  String toString() => 'ClientException(code: $code, message: $message)';
}

/// Validation error exception (HTTP 422 errors with validation_errors)
class ValidationException extends ApiException {
  final Map<String, List<String>>? validationErrors;

  const ValidationException({
    super.code,
    super.message,
    super.data,
    this.validationErrors,
  });

  @override
  String toString() =>
      'ValidationException(code: $code, message: $message, validationErrors: $validationErrors)';
}

/// Authentication/Authorization error exception (HTTP 401/403 errors)
class AuthException extends ApiException {
  const AuthException({super.code, super.message, super.data});

  @override
  String toString() => 'AuthException(code: $code, message: $message)';
}

/// Network connectivity exception
class NetworkException extends ApiException {
  const NetworkException({super.code, super.message, super.data});

  @override
  String toString() => 'NetworkException(code: $code, message: $message)';
}

/// Response parsing exception
class ParseException extends ApiException {
  final dynamic originalData;

  const ParseException({
    super.code,
    super.message,
    super.data,
    this.originalData,
  });

  @override
  String toString() => 'ParseException(code: $code, message: $message)';
}

/// Cache-related exception
class CacheException implements Exception {
  final String? message;

  const CacheException({this.message});

  @override
  String toString() => 'CacheException(message: $message)';
}

/// Unhandled exception wrapper
class UnhandledException implements Exception {
  final String? message;
  final Exception? originalException;

  const UnhandledException({this.message, this.originalException});

  @override
  String toString() =>
      'UnhandledException(message: $message, originalException: $originalException)';
}

/// Null response exception
class NullResponseException implements Exception {
  final String? message;

  const NullResponseException({this.message});

  @override
  String toString() => 'NullResponseException(message: $message)';
}

/// Business logic exception (e.g., underage user)
class BusinessLogicException implements Exception {
  final String? code;
  final String? message;

  const BusinessLogicException({this.code, this.message});

  @override
  String toString() => 'BusinessLogicException(code: $code, message: $message)';
}

/// Legacy exception for backward compatibility
class UnderageException extends BusinessLogicException {
  const UnderageException({super.code, super.message});

  @override
  String toString() => 'UnderageException(code: $code, message: $message)';
}
