# API Response Handling & Error Management System

This document explains the comprehensive API response handling and error management system that provides unified error handling across the application following clean architecture principles.

## Overview

This system provides comprehensive error handling that follows clean architecture principles:

1. **Data Sources** throw specific exceptions for different error types
2. **Repositories** catch these exceptions and convert them to `Failure` objects
3. **Domain Layer** receives `Either<Failure, Success>` results
4. **Presentation Layer** handles failures appropriately in the UI

The system uses an enhanced `Result<T>` class with flexible parsing capabilities and a robust exception hierarchy for proper error categorization.

## Key Changes

### 1. Enhanced Result<T> Class

The `Result<T>` class now includes:
- Detailed error information (message, code, exception)
- Helper methods for checking state (`isSuccess`, `isError`, `isLoading`)
- Data access methods (`data`, `errorMessage`, `errorCode`)
- Functional methods (`map`, `onSuccess`, `onError`)

### 2. Exception Hierarchy

A comprehensive exception hierarchy for proper error categorization:
- `ApiException` - Base class for all API-related exceptions
- `ServerException` - HTTP 5xx server errors
- `ClientException` - HTTP 4xx client errors
- `ValidationException` - HTTP 422 validation errors with detailed field errors
- `AuthException` - HTTP 401/403 authentication/authorization errors
- `NetworkException` - Network connectivity issues
- `ParseException` - Response parsing errors

### 3. Failure Hierarchy

Corresponding failure types that mirror the exception hierarchy:
- `ServerFailure`, `ClientFailure`, `ValidationFailure`, etc.
- Each failure includes error message, code, and specific error data

### 4. Enhanced ApiResponseParser Utility

Flexible parsing for different API response formats:
- `parseStandardResponse()` - For the standard format with status/data/error fields
- `parseDirectResponse()` - For direct data responses
- `parseListResponse()` - For array responses
- `parseWrappedListResponse()` - For wrapped array responses
- `parseFlexibleResponse()` - Automatically detects and parses different formats
- `parseFlexibleResponseWithErrors()` - **NEW**: Enhanced parser with error model support
- `parseCustomResponse()` - For completely custom parsing logic

### 5. Error Handler Utility

The `ApiErrorHandler` class provides utilities to convert `Result` errors to appropriate exceptions based on error codes and messages.

### 6. Simplified Data Sources

Data sources now return `Result<T>` directly and throw specific exceptions for different error types, which are then caught and converted to failures in the repository layer.

## Usage Examples

### Enhanced Data Source Implementation

```dart
class UserApiDataSourceImpl implements UserApiDataSource {
  final ApiClient _apiClient;

  @override
  Future<Result<LoginDataModel>> memberLogin({
    required String username,
    required String password,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberLogin,
      data: {'username': username, 'password': password},
    );

    return result.map<LoginDataModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Use enhanced parser with error model support
        final parseResult = ApiResponseParser.parseFlexibleResponseWithErrors<
          LoginDataModel, ValidationErrorsModel>(
          responseData,
          LoginDataModel.fromJson,
          errorFromJsonE: ValidationErrorsModel.fromJson,
          defaultErrorMessage: 'Failed to login',
        );

        if (parseResult.isSuccess) {
          return parseResult.data!;
        } else {
          // Use error handler to throw appropriate exception
          throw ApiErrorHandler.resultToException(parseResult);
        }
      } else {
        throw ParseException(message: 'Invalid response format: expected Map<String, dynamic>');
      }
    });
  }
}
```

### Enhanced Repository Implementation

```dart
@override
Future<Either<Failure, LoginData>> memberLogin({
  required String username,
  required String password,
}) async {
  if (await _networkInfo.isConnected) {
    try {
      final result = await _userApiDataSource.memberLogin(
        username: username,
        password: password,
      );

      if (result.isSuccess) {
        return Right(result.data!.toEntity());
      } else {
        return Left(ServerFailure(
          message: result.errorMessage ?? 'Unknown server error',
        ));
      }
    } on Exception catch (e) {
      // Use helper method to convert exceptions to appropriate failures
      return Left(_handleException(e));
    }
  } else {
    return Left(NetworkFailure(message: 'No internet connection'));
  }
}

// Helper method for exception-to-failure conversion
Failure _handleException(Exception e) {
  if (e is ValidationException) {
    return ValidationFailure(
      message: e.message ?? 'Validation failed',
      code: e.code,
      validationErrors: e.validationErrors,
    );
  } else if (e is AuthException) {
    return AuthFailure(
      message: e.message ?? 'Authentication failed',
      code: e.code,
    );
  } else if (e is ClientException) {
    return ClientFailure(
      message: e.message ?? 'Client error occurred',
      code: e.code,
    );
  } else if (e is ServerException) {
    return ServerFailure(
      message: e.message ?? 'Server error occurred',
      code: e.code,
    );
  } else if (e is NetworkException) {
    return NetworkFailure(
      message: e.message ?? 'Network error occurred',
      code: e.code,
    );
  } else if (e is ParseException) {
    return ParseFailure(
      message: e.message ?? 'Failed to parse response',
      code: e.code,
    );
  } else {
    return UnhandledFailure(message: e.toString(), code: null);
  }
}
```

### Different Response Format Examples

#### Standard API Response Format
```json
{
  "status": true,
  "error_message": null,
  "error_code": null,
  "data": {
    "id": 1,
    "name": "John Doe"
  },
  "api_version": "v.0.0.1"
}
```

```dart
final result = ApiResponseParser.parseStandardResponse(
  responseData,
  UserModel.fromJson,
);
```

#### Direct Data Response
```json
{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

```dart
final result = ApiResponseParser.parseDirectResponse(
  responseData,
  UserModel.fromJson,
);
```

#### List Response
```json
[
  {"id": 1, "name": "John"},
  {"id": 2, "name": "Jane"}
]
```

```dart
final result = ApiResponseParser.parseListResponse(
  responseData,
  UserModel.fromJson,
);
```

#### Flexible Parsing (Recommended)
```dart
// Automatically detects the format and parses accordingly
final result = ApiResponseParser.parseFlexibleResponse(
  responseData,
  UserModel.fromJson,
);
```

## Benefits

1. **Flexibility**: Supports any API response format without code changes
2. **Reduced Duplication**: Single `Result<T>` class handles all scenarios
3. **Better Error Handling**: Rich error information with codes, messages, and exceptions
4. **Functional Programming**: Map and callback methods for cleaner code
5. **Type Safety**: Strong typing throughout the chain
6. **Maintainability**: Centralized parsing logic in `ApiResponseParser`

## Migration Guide

### From ApiResponse to Result

**Old approach:**
```dart
Future<ApiResponse<LoginDataModel>> memberLogin() async {
  var result = await _apiClient.post<dynamic>(endpoint, data: data);
  if (result is SuccessState) {
    return ApiResponse.fromJson(result.value, LoginDataModel.fromJson);
  } else if (result is ErrorState) {
    final errorResponse = ApiResponse.fromJson(result.value, (json) => json);
    throw ServerException(
      code: errorResponse.errorCode,
      message: errorResponse.errorMessage,
    );
  }
}
```

**New approach:**
```dart
Future<Result<LoginDataModel>> memberLogin() async {
  final result = await _apiClient.post<dynamic>(endpoint, data: data);
  return result.map<LoginDataModel>((responseData) {
    final parseResult = ApiResponseParser.parseFlexibleResponse(
      responseData,
      LoginDataModel.fromJson,
    );
    if (parseResult.isSuccess) {
      return parseResult.data!;
    } else {
      throw ServerException(
        code: parseResult.errorCode,
        message: parseResult.errorMessage,
      );
    }
  });
}
```

### Error Response Format Support

#### Validation Error Response (HTTP 422)
```json
{
  "validation_errors": {
    "email": ["Email is required", "Email format is invalid"],
    "password": ["Password must be at least 8 characters"]
  }
}
```

#### Standard Error Response
```json
{
  "status": false,
  "error_message": "Invalid credentials",
  "error_code": "401",
  "data": null
}
```

#### Direct Error Response
```json
{
  "error_message": "Server error occurred",
  "error_code": "500"
}
```

## Error Code Mapping

| HTTP Status | Exception Type | Failure Type | Description |
|-------------|----------------|--------------|-------------|
| 200-299 | None | Success | Successful response |
| 400 | ClientException | ClientFailure | Bad request |
| 401 | AuthException | AuthFailure | Unauthorized |
| 403 | AuthException | AuthFailure | Forbidden |
| 422 | ValidationException | ValidationFailure | Validation errors |
| 500-599 | ServerException | ServerFailure | Server errors |
| Network | NetworkException | NetworkFailure | Connectivity issues |
| Parse | ParseException | ParseFailure | Response parsing errors |

## Best Practices

1. **Use enhanced parser**: Always use `parseFlexibleResponseWithErrors()` in data sources for comprehensive error handling
2. **Throw specific exceptions**: Use `ApiErrorHandler.resultToException()` to throw appropriate exception types
3. **Use helper methods**: Implement `_handleException()` in repositories to avoid code duplication
4. **Handle network connectivity**: Always check network status before making API calls
5. **Provide meaningful messages**: Include descriptive error messages for better user experience
6. **Log errors appropriately**: Use proper logging for debugging while protecting sensitive information
7. **Handle validation errors**: Extract field-specific validation errors for form validation
8. **Follow clean architecture**: Maintain proper separation between data sources, repositories, and domain layers
