import 'dart:developer' as developer;
import 'package:mcdc/core/error/exceptions.dart';

/// A generic API response parser that can handle different response formats
/// This parser focuses purely on data transformation and throws ParseException on errors
class ApiResponseParser {
  /// Parse a standard API response with the format:
  /// {
  ///   "status": true,
  ///   "error_message": null,
  ///   "error_code": null,
  ///   "data": {},
  ///   "api_version": "v.0.0.1"
  /// }
  static T parseStandardResponse<T>(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    try {
      final status = json['status'] as bool? ?? false;
      final errorMessage = json['error_message'] as String?;
      final errorCode = json['error_code']?.toString();
      final data = json['data'];

      if (status && data != null) {
        return fromJsonT(data as Map<String, dynamic>);
      } else {
        throw ParseException(
          message: errorMessage ?? 'API returned error status',
          code: errorCode ?? 'API_ERROR',
          originalData: json,
        );
      }
    } catch (e) {
      developer.log('Failed to parse standard response: ${e.toString()}');
      if (e is ParseException) {
        rethrow;
      }
      throw ParseException(
        message: 'Failed to parse standard response: ${e.toString()}',
        code: 'PARSE_ERROR',
        originalData: json,
      );
    }
  }

  /// Parse a direct data response (when the response is the data itself)
  /// Example: { "id": 1, "name": "John" }
  static T parseDirectResponse<T>(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    try {
      return fromJsonT(json);
    } catch (e) {
      developer.log('Failed to parse direct response: ${e.toString()}');
      throw ParseException(
        message: 'Failed to parse direct response: ${e.toString()}',
        code: 'DIRECT_PARSE_ERROR',
        originalData: json,
      );
    }
  }

  /// Parse a list response
  /// Example: [{ "id": 1, "name": "John" }, { "id": 2, "name": "Jane" }]
  static List<T> parseListResponse<T>(
    List<dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    try {
      return json
          .map((item) => fromJsonT(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      developer.log('Failed to parse list response: ${e.toString()}');
      throw ParseException(
        message: 'Failed to parse list response: ${e.toString()}',
        code: 'LIST_PARSE_ERROR',
        originalData: json,
      );
    }
  }

  /// Parse a wrapped list response
  /// Example: { "data": [{ "id": 1, "name": "John" }], "total": 10 }
  static List<T> parseWrappedListResponse<T>(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT, {
    String dataKey = 'data',
  }) {
    try {
      final data = json[dataKey] as List<dynamic>?;
      if (data != null) {
        return data
            .map((item) => fromJsonT(item as Map<String, dynamic>))
            .toList();
      } else {
        throw ParseException(
          message: 'No data found in response under key: $dataKey',
          code: 'MISSING_DATA_KEY',
          originalData: json,
        );
      }
    } catch (e) {
      developer.log('Failed to parse wrapped list response: ${e.toString()}');
      if (e is ParseException) {
        rethrow;
      }
      throw ParseException(
        message: 'Failed to parse wrapped list response: ${e.toString()}',
        code: 'WRAPPED_LIST_PARSE_ERROR',
        originalData: json,
      );
    }
  }

  /// Parse a custom response format
  /// This allows for complete flexibility in parsing any response format
  /// The custom parser should return the parsed data directly or throw an exception
  static T parseCustomResponse<T>(
    dynamic json,
    T Function(dynamic) customParser,
  ) {
    try {
      return customParser(json);
    } catch (e) {
      developer.log('Failed to parse custom response: ${e.toString()}');
      if (e is ParseException) {
        rethrow;
      }
      throw ParseException(
        message: 'Failed to parse custom response: ${e.toString()}',
        code: 'CUSTOM_PARSE_ERROR',
        originalData: json,
      );
    }
  }

  /// Parse a response that might be in different formats
  /// This method tries different parsing strategies
  static T parseFlexibleResponse<T>(
    dynamic json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    try {
      // If it's a Map, try different parsing strategies
      if (json is Map<String, dynamic>) {
        // Try standard format first
        if (json.containsKey('status') && json.containsKey('data')) {
          return parseStandardResponse(json, fromJsonT);
        }
        // Try direct format
        else {
          return parseDirectResponse(json, fromJsonT);
        }
      }
      // If it's a List, throw error as this method is for single objects
      else if (json is List) {
        throw ParseException(
          message: 'List response not supported for single object parsing',
          code: 'INVALID_FORMAT',
          originalData: json,
        );
      }
      // Unknown format
      else {
        throw ParseException(
          message: 'Unknown response format: ${json.runtimeType}',
          code: 'UNKNOWN_FORMAT',
          originalData: json,
        );
      }
    } catch (e) {
      developer.log('Failed to parse flexible response: ${e.toString()}');
      if (e is ParseException) {
        rethrow;
      }
      throw ParseException(
        message: 'Failed to parse flexible response: ${e.toString()}',
        code: 'FLEXIBLE_PARSE_ERROR',
        originalData: json,
      );
    }
  }
}
