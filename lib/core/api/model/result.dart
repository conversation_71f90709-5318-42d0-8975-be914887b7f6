/// A generic result wrapper that can handle success, error, and loading states
/// Supports any type of data and provides detailed error information
class Result<T> {
  Result._();

  factory Result.loading() = LoadingState<T>;

  factory Result.success(T value) = SuccessState<T>;

  factory Result.error({
    String? code,
    String? message,
    Exception? exception,
    dynamic responseData,
  }) = ErrorState<T>;

  /// Helper method to check if result is successful
  bool get isSuccess => this is SuccessState<T>;

  /// Helper method to check if result is error
  bool get isError => this is ErrorState<T>;

  /// Helper method to check if result is loading
  bool get isLoading => this is LoadingState<T>;

  /// Get the data if success, null otherwise
  T? get data {
    if (this is SuccessState<T>) {
      return (this as SuccessState<T>).value;
    }
    if (this is ErrorState<T>) {
      return (this as ErrorState<T>).responseData;
    }
    return null;
  }

  /// Get error message if error, null otherwise
  String? get errorMessage {
    if (this is ErrorState<T>) {
      return (this as ErrorState<T>).message;
    }
    return null;
  }

  /// Get error code if error, null otherwise
  String? get errorCode {
    if (this is ErrorState<T>) {
      return (this as ErrorState<T>).code;
    }
    return null;
  }

  /// Transform the result data using a mapper function
  /// Exceptions thrown in the mapper are caught and converted to error states
  Result<R> map<R>(R Function(T) mapper) {
    if (this is SuccessState<T>) {
      try {
        return Result.success(mapper((this as SuccessState<T>).value));
      } catch (e) {
        return Result.error(
          message: 'Mapping failed: ${e.toString()}',
          exception: e is Exception ? e : Exception(e.toString()),
        );
      }
    } else if (this is ErrorState<T>) {
      final errorState = this as ErrorState<T>;
      return Result<R>.error(
        message: errorState.message,
        code: errorState.code,
        exception: errorState.exception,
      );
    } else {
      return Result.loading();
    }
  }

  /// Transform the result using a function that returns a Result
  /// This is useful for chaining operations that might fail
  Result<R> flatMap<R>(Result<R> Function(T) mapper) {
    if (this is SuccessState<T>) {
      try {
        return mapper((this as SuccessState<T>).value);
      } catch (e) {
        return Result<R>.error(
          message: 'FlatMap operation failed: ${e.toString()}',
          exception: e is Exception ? e : Exception(e.toString()),
        );
      }
    } else if (this is ErrorState<T>) {
      final errorState = this as ErrorState<T>;
      return Result<R>.error(
        message: errorState.message,
        code: errorState.code,
        exception: errorState.exception,
      );
    } else {
      return Result<R>.loading();
    }
  }

  /// Execute a function when result is successful
  Result<T> onSuccess(void Function(T) callback) {
    if (this is SuccessState<T>) {
      callback((this as SuccessState<T>).value);
    }
    return this;
  }

  /// Execute a function when result is error
  Result<T> onError(void Function(String?, String?, Exception?) callback) {
    if (this is ErrorState<T>) {
      final errorState = this as ErrorState<T>;
      callback(errorState.message, errorState.code, errorState.exception);
    }
    return this;
  }
}

class LoadingState<T> extends Result<T> {
  LoadingState() : super._();
}

class ErrorState<T> extends Result<T> {
  ErrorState({this.code, this.message, this.exception, this.responseData})
    : super._();
  final String? code;
  final String? message;
  final Exception? exception;
  final dynamic responseData;

  @override
  String toString() {
    return 'ErrorState(code: $code, message: $message, responseData: $responseData)';
  }
}

class SuccessState<T> extends Result<T> {
  SuccessState(this.value) : super._();
  final T value;

  @override
  String toString() {
    return 'SuccessState(value: $value)';
  }
}
