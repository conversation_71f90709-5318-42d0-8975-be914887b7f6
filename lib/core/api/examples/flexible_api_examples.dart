// Examples of how to use the flexible API response system
// This file demonstrates different API response formats and how to handle them

import 'package:mcdc/core/api/model/result.dart';
import 'package:mcdc/core/api/model/api_response_parser.dart';

// Example model for demonstration
class ExampleModel {
  final int id;
  final String name;
  final String email;

  ExampleModel({required this.id, required this.name, required this.email});

  factory ExampleModel.fromJson(Map<String, dynamic> json) {
    return ExampleModel(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
    );
  }
}

class FlexibleApiExamples {
  /// Example 1: Standard API response format
  /// Response: {
  ///   "status": true,
  ///   "data": {"id": 1, "name": "John", "email": "<EMAIL>"},
  ///   "error_message": null,
  ///   "error_code": null,
  ///   "api_version": "v1.0"
  /// }
  static Result<ExampleModel> handleStandardResponse(
    Map<String, dynamic> json,
  ) {
    try {
      final data = ApiResponseParser.parseStandardResponse(
        json,
        ExampleModel.fromJson,
      );
      return Result.success(data);
    } catch (e) {
      return Result.error(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Example 2: Direct data response format
  /// Response: {"id": 1, "name": "John", "email": "<EMAIL>"}
  static Result<ExampleModel> handleDirectResponse(Map<String, dynamic> json) {
    try {
      final data = ApiResponseParser.parseDirectResponse(
        json,
        ExampleModel.fromJson,
      );
      return Result.success(data);
    } catch (e) {
      return Result.error(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Example 3: List response format
  /// Response: [
  ///   {"id": 1, "name": "John", "email": "<EMAIL>"},
  ///   {"id": 2, "name": "Jane", "email": "<EMAIL>"}
  /// ]
  static Result<List<ExampleModel>> handleListResponse(List<dynamic> json) {
    try {
      final data = ApiResponseParser.parseListResponse(
        json,
        ExampleModel.fromJson,
      );
      return Result.success(data);
    } catch (e) {
      return Result.error(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Example 4: Wrapped list response format
  /// Response: {
  ///   "data": [
  ///     {"id": 1, "name": "John", "email": "<EMAIL>"},
  ///     {"id": 2, "name": "Jane", "email": "<EMAIL>"}
  ///   ],
  ///   "total": 2,
  ///   "page": 1
  /// }
  static Result<List<ExampleModel>> handleWrappedListResponse(
    Map<String, dynamic> json,
  ) {
    try {
      final data = ApiResponseParser.parseWrappedListResponse(
        json,
        ExampleModel.fromJson,
      );
      return Result.success(data);
    } catch (e) {
      return Result.error(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Example 5: Custom response format
  /// Response: {
  ///   "success": true,
  ///   "result": {"id": 1, "name": "John", "email": "<EMAIL>"},
  ///   "message": "User retrieved successfully"
  /// }
  static Result<ExampleModel> handleCustomResponse(Map<String, dynamic> json) {
    return ApiResponseParser.parseCustomResponse(json, (data) {
      final jsonData = data as Map<String, dynamic>;
      final success = jsonData['success'] as bool? ?? false;
      final message = jsonData['message'] as String?;

      if (success && jsonData['result'] != null) {
        try {
          final model = ExampleModel.fromJson(
            jsonData['result'] as Map<String, dynamic>,
          );
          return Result.success(model);
        } catch (e) {
          return Result.error(
            message: 'Failed to parse result: ${e.toString()}',
            exception: e is Exception ? e : Exception(e.toString()),
          );
        }
      } else {
        return Result.error(message: message ?? 'Request failed');
      }
    });
  }

  /// Example 6: Flexible response (automatically detects format)
  /// This method can handle both standard and direct response formats
  static Result<ExampleModel> handleFlexibleResponse(dynamic json) {
    if (json is Map<String, dynamic>) {
      try {
        final data = ApiResponseParser.parseFlexibleResponse(
          json,
          ExampleModel.fromJson,
        );
        return Result.success(data);
      } catch (e) {
        return Result.error(
          message: e.toString(),
          exception: e is Exception ? e : Exception(e.toString()),
        );
      }
    } else {
      return Result.error(message: 'Invalid response format');
    }
  }

  /// Example 7: Using Result methods for functional programming
  static void demonstrateResultMethods() {
    // Create a sample result
    final result = Result.success(
      ExampleModel(id: 1, name: 'John Doe', email: '<EMAIL>'),
    );

    // Transform the result
    final nameResult = result.map<String>((model) => model.name);

    // Execute callbacks
    result
        .onSuccess((model) => print('Success: ${model.name}'))
        .onError((message, code, exception) => print('Error: $message'));

    // Check result state
    if (result.isSuccess) {
      print('Data: ${result.data?.name}');
    } else if (result.isError) {
      print('Error: ${result.errorMessage}');
    }
  }

  /// Example 8: Error handling in data sources
  static Result<ExampleModel> handleResponseWithErrorHandling(
    dynamic responseData,
  ) {
    try {
      if (responseData is Map<String, dynamic>) {
        // Parser now returns data directly and throws ParseException on errors
        final data = ApiResponseParser.parseFlexibleResponse(
          responseData,
          ExampleModel.fromJson,
        );
        return Result.success(data);
      } else {
        return Result.error(
          message: 'Invalid response format: expected Map<String, dynamic>',
        );
      }
    } catch (e) {
      return Result.error(
        message: 'Unexpected error: ${e.toString()}',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Example 9: Pagination response format
  /// Response: {
  ///   "data": [...],
  ///   "pagination": {
  ///     "current_page": 1,
  ///     "total_pages": 5,
  ///     "total_items": 50
  ///   }
  /// }
  static Result<List<ExampleModel>> handlePaginatedResponse(
    Map<String, dynamic> json,
  ) {
    return ApiResponseParser.parseCustomResponse(json, (data) {
      final jsonData = data as Map<String, dynamic>;
      final dataList = jsonData['data'] as List<dynamic>?;

      if (dataList != null) {
        try {
          final models =
              dataList
                  .map(
                    (item) =>
                        ExampleModel.fromJson(item as Map<String, dynamic>),
                  )
                  .toList();
          return Result.success(models);
        } catch (e) {
          return Result.error(
            message: 'Failed to parse paginated data: ${e.toString()}',
            exception: e is Exception ? e : Exception(e.toString()),
          );
        }
      } else {
        return Result.error(message: 'No data found in paginated response');
      }
    });
  }
}
