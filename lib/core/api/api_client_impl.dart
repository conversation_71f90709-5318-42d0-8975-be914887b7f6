import 'dart:developer' as developer;

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:mcdc/core/api/api_client.dart';
import 'package:mcdc/core/api/api_endpoints.dart';
import 'package:mcdc/core/api/model/result.dart';
import 'package:mcdc/features/user/data/datasources/auth_storage_data_source.dart';

class ApiClientImpl extends ApiClient {
  final _connectTimeout = 30000;
  final _receiveTimeout = 30000;
  final AuthStorageDataSource _authStorage;
  late Dio _dio;

  ApiClientImpl({String? baseUrl, required AuthStorageDataSource authStorage})
    : _authStorage = authStorage {
    if (baseUrl != null) {
      if (!baseUrl.startsWith("http")) {
        baseUrl = "https://$baseUrl";
      }
    } else {
      baseUrl = ApiEndpoints.baseUrl;
    }
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        headers: {Headers.contentTypeHeader: Headers.jsonContentType},
        connectTimeout: Duration(milliseconds: _connectTimeout),
        receiveTimeout: Duration(milliseconds: _receiveTimeout),
      ),
    );
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          try {
            options.headers['Content-Type'] = 'application/json';
            options.headers['Accept-Language'] =
                'th'; //TODO: change to language from app language

            // Add Bearer token from secure storage
            final accessToken = await _authStorage.getAccessToken();
            if (accessToken != null && accessToken.isNotEmpty) {
              options.headers['Authorization'] = 'Bearer $accessToken';
            }

            if (options.headers['X-API-KEY'] == null) {
              options.headers['X-API-KEY'] = dotenv.get('X_API_KEY');
            }
          } catch (e) {
            return handler.reject(DioException(requestOptions: options));
          }
          return handler.next(options); //continue
        } /*
        onError: (error, handler) {
          developer.log(
            'from<== ${error.requestOptions.baseUrl}${error.requestOptions.path}',
          );
          developer.log('error=${error.toString()}');
          // developer.log('error=${error.response?.data}');
          handler.next(error);
        },*/,
      ),
    );
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestHeader: true,
          requestBody: true,
          responseHeader: true,
          responseBody: true,
          error: true,
        ),
      );
    }
  }

  Result<T> _handleResponse<T>(Response<dynamic> response) {
    final responseStatus = response.statusCode;
    developer.log("status=$responseStatus");
    developer.log('response=$response');

    if (responseStatus == 200 || responseStatus == 201) {
      // For spreadsheet files, return the raw bytes
      if (response.headers.map['content-type']?.contains('spreadsheetml') ==
          true) {
        return Result.success(response.data as T);
      }

      return Result.success(response.data as T);
    } else {
      return Result.error(
        code: responseStatus.toString(),
        message: 'HTTP Error: $responseStatus',
        responseData: response.data,
      );
    }
  }

  @override
  Future<Result<T>> post<T>(
    String path, {
    dynamic data,
    String? baseUrl,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      if (baseUrl != null) {
        if (!baseUrl.startsWith("http")) {
          baseUrl = "https://$baseUrl";
        }
        _dio.options.baseUrl = baseUrl;
      } else {
        baseUrl = ApiEndpoints.baseUrl;
      }
      _dio.options.baseUrl = baseUrl;
      var response = await _dio.post<dynamic>(
        path,
        data: data,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse<T>(response);
    } on DioException catch (e) {
      return Result.error(
        code: e.response?.statusCode?.toString(),
        message: e.message ?? 'Network error occurred',
        exception: e,
        responseData: e.response?.data,
      );
    } catch (e) {
      developer.log('error=$e');
      return Result.error(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<Result<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    String? baseUrl,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      if (baseUrl != null) {
        if (!baseUrl.startsWith("http")) {
          baseUrl = "https://$baseUrl";
        }
      } else {
        baseUrl = ApiEndpoints.baseUrl;
      }
      _dio.options.baseUrl = baseUrl;
      var response = await _dio.get<dynamic>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse<T>(response);
    } on DioException catch (e) {
      return Result.error(
        code: e.response?.statusCode?.toString(),
        message: e.message ?? 'Network error occurred',
        exception: e,
        responseData: e.response?.data,
      );
    } catch (e) {
      developer.log('error=$e');
      return Result.error(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  @override
  Future<Result<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    String? baseUrl,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      if (baseUrl != null) {
        if (!baseUrl.startsWith("http")) {
          baseUrl = "https://$baseUrl";
        }
      } else {
        baseUrl = ApiEndpoints.baseUrl;
      }
      _dio.options.baseUrl = baseUrl;
      var response = await _dio.patch<dynamic>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse<T>(response);
    } on DioException catch (e) {
      return Result.error(
        code: e.response?.statusCode?.toString(),
        message: e.message ?? 'Network error occurred',
        exception: e,
        responseData: e.response?.data,
      );
    } catch (e) {
      developer.log('error=$e');
      return Result.error(
        message: e.toString(),
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }
}
