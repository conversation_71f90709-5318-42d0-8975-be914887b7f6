import 'package:mcdc/core/api/api_client.dart';
import 'package:mcdc/core/api/api_endpoints.dart';
import 'package:mcdc/core/api/model/result.dart';
import 'package:mcdc/core/api/model/api_response_parser.dart';
import 'package:mcdc/core/error/exceptions.dart';
import '../models/send_otp_response_model.dart';
import '../models/verify_otp_response_model.dart';

abstract class OtpApiDataSource {
  Future<Result<SendOtpResponseModel>> sendOtp({required String email});

  Future<Result<VerifyOtpResponseModel>> verifyOtp({
    required String token,
    required String otp,
    required String refCode,
  });
}

class OtpApiDataSourceImpl implements OtpApiDataSource {
  final ApiClient _apiClient;

  const OtpApiDataSourceImpl(this._apiClient);

  @override
  Future<Result<SendOtpResponseModel>> sendOtp({required String email}) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.otpGenerate,
      data: {'email': email},
    );

    // Use the Result-based approach with updated parsing behavior
    return result.map<SendOtpResponseModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Parser now returns data directly and throws ParseException on errors
        return ApiResponseParser.parseFlexibleResponse(
          responseData,
          SendOtpResponseModel.fromJson,
        );
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }

  @override
  Future<Result<VerifyOtpResponseModel>> verifyOtp({
    required String token,
    required String otp,
    required String refCode,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.otpVerify,
      data: {'token': token, 'otp': otp, 'ref_code': refCode},
    );

    // Use the Result-based approach with updated parsing behavior
    return result.map<VerifyOtpResponseModel>((responseData) {
      if (responseData is Map<String, dynamic>) {
        // Parser now returns data directly and throws ParseException on errors
        return ApiResponseParser.parseFlexibleResponse(
          responseData,
          VerifyOtpResponseModel.fromJson,
        );
      } else {
        throw ServerException(message: 'Invalid response format');
      }
    });
  }
}
